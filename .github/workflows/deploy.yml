name: Deploy Pipeline

on:
  push:
    branches: 
      - main
      - development

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest
    
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'development' }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Run tests
      run: pnpm test
      
    - name: Build frontend
      run: pnpm --dir intentional-fe build
      
    - name: Deploy to ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}
      if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/development'
      run: |
        echo "Deploying to ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }} environment"
        # Deployment commands would go here
        # For example, for AWS S3 deployment:
        # aws s3 sync intentional-fe/dist/ s3://your-bucket-name --delete
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        
  deploy-backend:
    runs-on: ubuntu-latest
    
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'development' }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18.x'
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Run tests
      run: pnpm test
      
    - name: Deploy backend
      run: |
        echo "Deploying backend to ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }} environment"
        # Backend deployment commands would go here
        # For example, for AWS CDK deployment:
        # cd intentional-be && pnpm cdk deploy
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
