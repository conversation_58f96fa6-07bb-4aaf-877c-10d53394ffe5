name: CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x]
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Run linting
      run: pnpm lint
      
    - name: Run type checking
      run: pnpm type-check
      
    - name: Run tests
      run: pnpm test
      
    - name: Build frontend
      run: pnpm --dir intentional-fe build
      
    - name: Build backend
      run: pnpm --dir intentional-be build
